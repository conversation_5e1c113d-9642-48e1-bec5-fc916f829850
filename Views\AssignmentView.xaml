<UserControl x:Class="DriverManagementSystem.Views.AssignmentView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DriverManagementSystem.Views"
             FlowDirection="LeftToRight"
             Background="White">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/PrintStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Converter للتحقق من وجود الصور -->
            <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>

            <!-- Converter مخصص للتحقق من وجود النص/الصورة -->
            <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>

            <!-- Converter للتحقق من النص الفارغ -->
            <local:EmptyStringToVisibilityConverter x:Key="EmptyStringToVisibilityConverter"/>

            <!-- Converter للرقم التسلسلي -->
            <local:RowIndexConverter x:Key="RowIndexConverter"/>

        </ResourceDictionary>
    </UserControl.Resources>

    <!-- الإطار الخارجي الرسمي بحجم A4 -->
    <Border BorderBrush="#2C3E50" BorderThickness="2" Margin="15" Background="White"
            Width="650" Height="900" HorizontalAlignment="Center" VerticalAlignment="Top"
            CornerRadius="5">
        <Border.Effect>
            <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="3" Opacity="0.3"/>
        </Border.Effect>

        <!-- Grid للتخطيط مع Footer ثابت -->
        <Grid Background="Transparent" Margin="25,25,25,10">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- المحتوى الرئيسي -->
            <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto">
                <StackPanel FlowDirection="LeftToRight">

                <!-- الهيدر الرسمي -->
            <Border Background="#F8F9FA" BorderBrush="#2C3E50" BorderThickness="0,0,0,2"
                    Padding="15" Margin="0,0,0,25" CornerRadius="5,5,0,0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- النصوص الإنجليزية -->
                    <StackPanel Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Left">
                        <TextBlock Text="Social Fund For Development"
                                   FontSize="13" FontWeight="Bold"
                                   Foreground="#2C3E50" Margin="0,2"
                                   TextAlignment="Left"/>
                        <TextBlock Text="Republic OF YEMEN"
                                   FontSize="12" FontWeight="SemiBold"
                                   Foreground="#34495E" Margin="0,1"
                                   TextAlignment="Left"/>
                        <TextBlock Text="Presidency of Council of Ministers"
                                   FontSize="11"
                                   Foreground="#5D6D7E" Margin="0,1"
                                   TextAlignment="Left"/>
                        <TextBlock Text="Dhamar &amp; Albidaa Branch"
                                   FontSize="11"
                                   Foreground="#5D6D7E" Margin="0,1"
                                   TextAlignment="Left"/>
                    </StackPanel>

                    <!-- الشعار -->
                    <Border Grid.Column="1" Background="White" CornerRadius="50"
                            Width="100" Height="100" BorderBrush="#2C3E50" BorderThickness="2">
                        <Image Source="../icons/sfd.png"
                               Width="80" Height="80"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Center"/>
                    </Border>

                    <!-- النصوص العربية -->
                    <StackPanel Grid.Column="2" VerticalAlignment="Center" HorizontalAlignment="Right">
                        <TextBlock Text="الجمهورية اليمنية"
                                   FontSize="13" FontWeight="Bold"
                                   Foreground="#2C3E50" Margin="0,2"
                                   TextAlignment="Right"/>
                        <TextBlock Text="رئاسة مجلس الوزراء"
                                   FontSize="12" FontWeight="SemiBold"
                                   Foreground="#34495E" Margin="0,1"
                                   TextAlignment="Right"/>
                        <TextBlock Text="الصندوق الاجتماعي للتنمية"
                                   FontSize="11"
                                   Foreground="#5D6D7E" Margin="0,1"
                                   TextAlignment="Right"/>
                        <TextBlock Text="فرع ذمار البيضاء"
                                   FontSize="11"
                                   Foreground="#5D6D7E" Margin="0,1"
                                   TextAlignment="Right"/>
                    </StackPanel>
                </Grid>
            </Border>

                <!-- العنوان والتواريخ الرسمية -->
                <Grid Margin="0,0,0,20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- العنوان الرئيسي -->
                    <Border Grid.Row="0" Background="#2C3E50" CornerRadius="10"
                            Padding="25,12" HorizontalAlignment="Center" Margin="0,0,0,15">
                        <Border.Effect>
                            <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2" Opacity="0.4"/>
                        </Border.Effect>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📋" FontSize="20" Margin="0,0,8,0" Foreground="White"/>
                            <TextBlock Text="تكليف رسمي" FontSize="20" FontWeight="Bold"
                                       Foreground="White" TextAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- التواريخ -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- التاريخ الهجري -->
                        <Border Grid.Column="0" Background="#ECF0F1" BorderBrush="#BDC3C7"
                                BorderThickness="1" CornerRadius="5" Padding="10" Margin="0,0,10,0">
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="📅 التاريخ الهجري" FontSize="12" FontWeight="Bold"
                                           Foreground="#2C3E50" TextAlignment="Center"/>
                                <TextBlock Text="{Binding HijriDate, FallbackValue=____/__/__}"
                                           FontSize="13" Foreground="#34495E" TextAlignment="Center"
                                           FontWeight="SemiBold" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>

                        <!-- التاريخ الميلادي -->
                        <Border Grid.Column="1" Background="#ECF0F1" BorderBrush="#BDC3C7"
                                BorderThickness="1" CornerRadius="5" Padding="10" Margin="10,0,0,0">
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="📅 التاريخ الميلادي" FontSize="12" FontWeight="Bold"
                                           Foreground="#2C3E50" TextAlignment="Center"/>
                                <TextBlock Text="{Binding StartDate, FallbackValue=____/__/__}"
                                           FontSize="13" Foreground="#34495E" TextAlignment="Center"
                                           FontWeight="SemiBold" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </Grid>

                <!-- النص التمهيدي الرسمي -->
                <Border Background="#E8F4FD" BorderBrush="#3498DB" BorderThickness="1"
                        CornerRadius="8" Padding="20" Margin="0,0,0,20">
                    <TextBlock Text="يُكلف الصندوق الاجتماعي للتنمية فرع ذمار والبيضاء الأخ المُبين اسمه في الجدول أدناه لتنفيذ المهمة التالية:"
                               FlowDirection="RightToLeft" TextWrapping="Wrap" FontSize="16"
                               FontWeight="Bold" Foreground="#2C3E50"
                               TextAlignment="Center" LineHeight="25"/>
                </Border>

                <!-- معلومات التكليف بشكل نقاط -->
                <StackPanel Margin="0,0,0,20" FlowDirection="RightToLeft" Height="114">

                    <!-- اسم المشروع -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <TextBlock Text="•" FontSize="16" FontWeight="Bold" Foreground="#2196F3"
                                   Margin="0,0,10,0" VerticalAlignment="Top"/>
                        <StackPanel MaxWidth="500">
                            <TextBlock FontSize="12" Foreground="#495057" TextWrapping="Wrap" FlowDirection="RightToLeft"><Run Text="اسم المشروع: " FontWeight="Bold"/><Run Text=" "/><Run Text="{Binding ProjectName}" Foreground="#2196F3"/></TextBlock>
                        </StackPanel>
                    </StackPanel>

                    <!-- النشاط -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <TextBlock Text="•" FontSize="16" FontWeight="Bold" Foreground="#28A745"
                                   Margin="0,0,10,0" VerticalAlignment="Top"/>
                        <TextBlock FontSize="12" Foreground="#495057" TextWrapping="Wrap" FlowDirection="RightToLeft"><Run Text="النشاط: " FontWeight="Bold"/><Run Text=" "/><Run Text="{Binding ActivityName}" Foreground="#28A745"/></TextBlock>
                    </StackPanel>

                    <!-- خط السير للنقاط الأمنية -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <TextBlock Text="•" FontSize="16" FontWeight="Bold" Foreground="#FD7E14"
                                   Margin="0,0,10,0" VerticalAlignment="Top"/>
                        <TextBlock FontSize="12" Foreground="#495057" TextWrapping="Wrap" FlowDirection="RightToLeft"><Run Text="خط السير: " FontWeight="Bold"/><Run Text=" "/><Run Text="{Binding RouteName}" Foreground="#FD7E14"/></TextBlock>
                    </StackPanel>

                    <!-- تاريخ النزول والعودة -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <TextBlock Text="•" FontSize="16" FontWeight="Bold" Foreground="#DC3545"
                                   Margin="0,0,10,0" VerticalAlignment="Top"/>
                        <TextBlock FontSize="12" Foreground="#495057" FlowDirection="RightToLeft"><Run Text="تاريخ النزول من: " FontWeight="Bold"/><Run Text=" "/><Run Text="{Binding StartDate}" Foreground="#DC3545" FontWeight="Bold"/><Run Text=" "/><Run Text="      حتى: " FontWeight="Bold"/><Run Text=" "/><Run Text="{Binding EndDate}" Foreground="#DC3545" FontWeight="Bold"/></TextBlock>
                    </StackPanel>

                </StackPanel>

                <!-- جدول المكلفين المحسن -->
                <Border Background="White" BorderBrush="#2C3E50" BorderThickness="2"
                        CornerRadius="8" Padding="0" Margin="0,0,0,20">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2" Opacity="0.2"/>
                    </Border.Effect>
                    <StackPanel>
                        <!-- عنوان الجدول -->
                        <Border Background="#2C3E50" BorderBrush="#2C3E50" BorderThickness="0,0,0,2"
                                Padding="15" CornerRadius="8,8,0,0">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <TextBlock Text="👥" FontSize="16" Margin="0,0,8,0" Foreground="White"/>
                                <TextBlock Text="القائمون بالزيارة" FontSize="16" FontWeight="Bold"
                                           Foreground="White"/>
                            </StackPanel>
                        </Border>

                        <!-- الجدول المحسن -->
                        <DataGrid ItemsSource="{Binding Assignees}" AutoGenerateColumns="False"
                                  HeadersVisibility="Column" FontSize="13" RowHeight="35"
                                  BorderThickness="0" GridLinesVisibility="All"
                                  HorizontalGridLinesBrush="#BDC3C7" VerticalGridLinesBrush="#BDC3C7"
                                  Background="White" AlternatingRowBackground="#F8F9FA"
                                  CanUserAddRows="False" CanUserDeleteRows="False"
                                  MaxHeight="120" ScrollViewer.VerticalScrollBarVisibility="Auto"
                                  Margin="5"
                                  SelectionMode="Single" IsReadOnly="True">
                            <DataGrid.Columns>
                                <!-- الصفة -->
                                <DataGridTextColumn Header="الصفة" Binding="{Binding Position}" Width="120">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="#34495E"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="13"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="8"/>
                                            <Setter Property="BorderBrush" Value="#2C3E50"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                            <Setter Property="Foreground" Value="#2C3E50"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- نوع الهوية -->
                                <DataGridTextColumn Header="نوع الهوية" Binding="{Binding CardType}" Width="110">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="#34495E"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="13"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="8"/>
                                            <Setter Property="BorderBrush" Value="#2C3E50"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                            <Setter Property="Foreground" Value="#2C3E50"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- رقم الهوية -->
                                <DataGridTextColumn Header="رقم الهوية" Binding="{Binding CardNumber}" Width="130">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="#34495E"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="13"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="8"/>
                                            <Setter Property="BorderBrush" Value="#2C3E50"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                            <Setter Property="Foreground" Value="#2C3E50"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- رقم التلفون -->
                                <DataGridTextColumn Header="رقم التلفون" Binding="{Binding PhoneNumber}" Width="130">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="#34495E"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="13"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="8"/>
                                            <Setter Property="BorderBrush" Value="#2C3E50"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                            <Setter Property="Foreground" Value="#2C3E50"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- الاسم الكامل -->
                                <DataGridTextColumn Header="الاسم الكامل" Binding="{Binding Name}" Width="*" MinWidth="200">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="#34495E"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="13"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="8"/>
                                            <Setter Property="BorderBrush" Value="#2C3E50"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Right"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="Margin" Value="8,0"/>
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                            <Setter Property="Foreground" Value="#2C3E50"/>
                                            <Setter Property="TextWrapping" Value="Wrap"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- م -->
                                <DataGridTextColumn Header="م" Width="50">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="#34495E"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="13"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="8"/>
                                            <Setter Property="BorderBrush" Value="#2C3E50"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.Binding>
                                        <Binding RelativeSource="{RelativeSource AncestorType=DataGridRow}"
                                                 Converter="{StaticResource RowIndexConverter}"/>
                                    </DataGridTextColumn.Binding>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="Foreground" Value="#2C3E50"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- جدول المركبات المحسن -->
                <Border Background="White" BorderBrush="#2C3E50" BorderThickness="2"
                        CornerRadius="8" Padding="0" Margin="0,0,0,25">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2" Opacity="0.2"/>
                    </Border.Effect>
                    <StackPanel>
                        <!-- عنوان الجدول -->
                        <Border Background="#2C3E50" BorderBrush="#2C3E50" BorderThickness="0,0,0,2"
                                Padding="15" CornerRadius="8,8,0,0">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <TextBlock Text="🚗" FontSize="16" Margin="0,0,8,0" Foreground="White"/>
                                <TextBlock Text="وسائل النقل والسائقون" FontSize="16" FontWeight="Bold"
                                           Foreground="White"/>
                            </StackPanel>
                        </Border>

                        <!-- الجدول المحسن -->
                        <DataGrid ItemsSource="{Binding Vehicles}" AutoGenerateColumns="False"
                                  HeadersVisibility="Column" FontSize="13" RowHeight="35"
                                  BorderThickness="0" GridLinesVisibility="All"
                                  VerticalGridLinesBrush="#BDC3C7"
                                  HorizontalGridLinesBrush="#BDC3C7" Background="White"
                                  AlternatingRowBackground="#F8F9FA"
                                  CanUserAddRows="False" CanUserDeleteRows="False"
                                  MaxHeight="120" ScrollViewer.VerticalScrollBarVisibility="Auto"
                                  Margin="5"
                                  SelectionMode="Single" IsReadOnly="True">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="رقم اللوحة" Binding="{Binding PlateNumber}" Width="120">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="#34495E"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="13"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="8"/>
                                            <Setter Property="BorderBrush" Value="#2C3E50"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                            <Setter Property="Foreground" Value="#2C3E50"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="نوع السيارة" Binding="{Binding VehicleType}" Width="120">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="#34495E"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="13"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="8"/>
                                            <Setter Property="BorderBrush" Value="#2C3E50"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                            <Setter Property="Foreground" Value="#2C3E50"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="رقم هوية السائق" Binding="{Binding DriverId}" Width="140">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="#34495E"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="13"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="8"/>
                                            <Setter Property="BorderBrush" Value="#2C3E50"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                            <Setter Property="Foreground" Value="#2C3E50"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="130">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="#34495E"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="13"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="8"/>
                                            <Setter Property="BorderBrush" Value="#2C3E50"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                            <Setter Property="Foreground" Value="#2C3E50"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="اسم السائق الكامل" Binding="{Binding DriverName}" Width="*" MinWidth="200">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="#34495E"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="13"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="8"/>
                                            <Setter Property="BorderBrush" Value="#2C3E50"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Right"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="Margin" Value="8,0"/>
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                            <Setter Property="Foreground" Value="#2C3E50"/>
                                            <Setter Property="TextWrapping" Value="Wrap"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <DataGridTextColumn Header="م" Binding="{Binding Index}" Width="50">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="#34495E"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="13"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="8"/>
                                            <Setter Property="BorderBrush" Value="#2C3E50"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="Foreground" Value="#2C3E50"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>





                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- النص التعاوني الرسمي -->
                <Border Background="#E8F6F3" BorderBrush="#27AE60" BorderThickness="1"
                        CornerRadius="8" Padding="20" Margin="0,15,0,20">
                    <TextBlock Text="وعليه: تكرموا مشكورين بالتعاون مع المذكور لما فيه المصلحة العامة وخدمة المجتمع."
                               FlowDirection="RightToLeft" TextWrapping="Wrap" FontSize="15"
                               FontWeight="Bold" Foreground="#2C3E50"
                               TextAlignment="Center" LineHeight="25"/>
                </Border>

                <!-- التوقيع الرسمي -->
                <Border Background="#F8F9FA" BorderBrush="#BDC3C7" BorderThickness="1"
                        CornerRadius="8" Padding="25" Margin="0,20,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- مدير الفرع -->
                        <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                            <Border Background="White" BorderBrush="#2C3E50" BorderThickness="1"
                                    CornerRadius="5" Padding="15,10" Margin="0,0,0,15">
                                <TextBlock Text="مدير الفرع" FontWeight="Bold" FontSize="14"
                                           Foreground="#2C3E50" TextAlignment="Center"/>
                            </Border>
                            <TextBlock Text="م/محمد محمد الديلمي" FontWeight="Bold" FontSize="14"
                                       Foreground="#2C3E50" TextAlignment="Center"/>
                        </StackPanel>

                        <!-- التوقيع -->
                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                            <Border Background="White" BorderBrush="#2C3E50" BorderThickness="1"
                                    CornerRadius="5" Padding="15,10" Margin="0,0,0,15">
                                <TextBlock Text="التوقيع" FontWeight="Bold" FontSize="14"
                                           Foreground="#2C3E50" TextAlignment="Center"/>
                            </Border>
                            <Border BorderBrush="#BDC3C7" BorderThickness="0,0,0,2"
                                    Width="150" Height="30"/>
                        </StackPanel>
                    </Grid>
                </Border>

                </StackPanel>
            </ScrollViewer>

            <!-- Footer رسمي ثابت في الأسفل -->
            <Border Grid.Row="1" Background="#2C3E50" BorderBrush="#2C3E50"
                    CornerRadius="0,0,5,5" Padding="20" Margin="0,15,0,0">
                <StackPanel>
                    <!-- النص العربي -->
                    <TextBlock Text="الصندوق الاجتماعي للتنمية - فرع ذمار - خط صنعاء تعز - جولة كمران - عمارة الأوقاف - هاتف : 503045 / فاكس: 503047 / ص.ب: 87210"
                               FontSize="11" FontWeight="Bold" TextAlignment="Center"
                               Foreground="White" Margin="0,3" FlowDirection="RightToLeft"
                               TextWrapping="Wrap"/>

                    <TextBlock Text="الرقم المجاني للشكاوي 8009800 أو الرقم المباشر بالشكاوي 770959624."
                               FontSize="10" TextAlignment="Center"
                               Foreground="#ECF0F1" Margin="0,3" FlowDirection="RightToLeft"/>

                    <!-- النص الإنجليزي -->
                    <TextBlock FontSize="10" TextAlignment="Center" Foreground="#ECF0F1"
                               Margin="0,3" FlowDirection="LeftToRight" TextWrapping="Wrap">
                        <Run Text="Social Fund for Development / Dhamar Branch – Taiz Sana'a Street- Camran Round – P.O. Box: 87400 "/>
                        <Run Text="Tel: 503045" Foreground="#3498DB" TextDecorations="Underline"/>
                        <Run Text=" – Fax: 503047 – Free number: 8009800 – Email: <EMAIL>"/>
                    </TextBlock>
                </StackPanel>
            </Border>

        </Grid>
    </Border>
</UserControl>
